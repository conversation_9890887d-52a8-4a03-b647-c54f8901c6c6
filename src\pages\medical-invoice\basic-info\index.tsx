import {
  PageContainer,
  ProCard,
  ProTable,
  ProForm,
  ProFormText,
  ProFormTextArea,
  ProFormSelect,
} from "@ant-design/pro-components";
import { Button, message, Modal, Space, Tabs } from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined } from "@ant-design/icons";
import React, { useState, useRef } from "react";
import type { ActionType, ProColumns } from "@ant-design/pro-components";

// 定义数据类型
interface OrganizationInfo {
  id: string;
  name: string;
  creditCode: string;
  address: string;
  contact: string;
  phone: string;
  type: string;
  status: string;
  createTime: string;
}

interface DepartmentInfo {
  id: string;
  code: string;
  name: string;
  type: string;
  parentId?: string;
  description: string;
  status: string;
  createTime: string;
}

interface ChargeItemInfo {
  id: string;
  code: string;
  name: string;
  category: string;
  price: number;
  unit: string;
  description: string;
  status: string;
  createTime: string;
}

const BasicInfoManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState("organization");
  const [modalVisible, setModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState<any>(null);
  const [modalType, setModalType] = useState<"add" | "edit">("add");
  const actionRef = useRef<ActionType>();

  // 模拟数据
  const mockOrganizationData: OrganizationInfo[] = [
    {
      id: "1",
      name: "北京市第一人民医院",
      creditCode: "12110000400000123X",
      address: "北京市朝阳区建国路1号",
      contact: "张三",
      phone: "010-12345678",
      type: "三甲医院",
      status: "正常",
      createTime: "2024-01-01 10:00:00",
    },
  ];

  const mockDepartmentData: DepartmentInfo[] = [
    {
      id: "1",
      code: "NEI01",
      name: "内科",
      type: "临床科室",
      description: "内科诊疗科室",
      status: "正常",
      createTime: "2024-01-01 10:00:00",
    },
    {
      id: "2",
      code: "WAI01",
      name: "外科",
      type: "临床科室",
      description: "外科诊疗科室",
      status: "正常",
      createTime: "2024-01-01 10:00:00",
    },
  ];

  const mockChargeItemData: ChargeItemInfo[] = [
    {
      id: "1",
      code: "GHFEI001",
      name: "普通门诊挂号费",
      category: "挂号费",
      price: 5.0,
      unit: "次",
      description: "普通门诊挂号收费项目",
      status: "正常",
      createTime: "2024-01-01 10:00:00",
    },
    {
      id: "2",
      code: "JCFEI001",
      name: "血常规检查",
      category: "检查费",
      price: 25.0,
      unit: "次",
      description: "血常规化验检查费用",
      status: "正常",
      createTime: "2024-01-01 10:00:00",
    },
  ];

  // 机构信息列配置
  const organizationColumns: ProColumns<OrganizationInfo>[] = [
    {
      title: "机构名称",
      dataIndex: "name",
      key: "name",
      width: 200,
    },
    {
      title: "统一社会信用代码",
      dataIndex: "creditCode",
      key: "creditCode",
      width: 180,
    },
    {
      title: "机构地址",
      dataIndex: "address",
      key: "address",
      width: 250,
      ellipsis: true,
    },
    {
      title: "联系人",
      dataIndex: "contact",
      key: "contact",
      width: 100,
    },
    {
      title: "联系电话",
      dataIndex: "phone",
      key: "phone",
      width: 120,
    },
    {
      title: "机构类型",
      dataIndex: "type",
      key: "type",
      width: 100,
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 80,
      valueEnum: {
        正常: { text: "正常", status: "Success" },
        停用: { text: "停用", status: "Error" },
      },
    },
    {
      title: "创建时间",
      dataIndex: "createTime",
      key: "createTime",
      width: 150,
      valueType: "dateTime",
    },
    {
      title: "操作",
      key: "action",
      width: 150,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  // 科室信息列配置
  const departmentColumns: ProColumns<DepartmentInfo>[] = [
    {
      title: "科室代码",
      dataIndex: "code",
      key: "code",
      width: 120,
    },
    {
      title: "科室名称",
      dataIndex: "name",
      key: "name",
      width: 150,
    },
    {
      title: "科室类型",
      dataIndex: "type",
      key: "type",
      width: 120,
      valueEnum: {
        临床科室: { text: "临床科室", status: "Processing" },
        医技科室: { text: "医技科室", status: "Success" },
        行政科室: { text: "行政科室", status: "Default" },
      },
    },
    {
      title: "科室描述",
      dataIndex: "description",
      key: "description",
      width: 200,
      ellipsis: true,
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 80,
      valueEnum: {
        正常: { text: "正常", status: "Success" },
        停用: { text: "停用", status: "Error" },
      },
    },
    {
      title: "创建时间",
      dataIndex: "createTime",
      key: "createTime",
      width: 150,
      valueType: "dateTime",
    },
    {
      title: "操作",
      key: "action",
      width: 150,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  // 收费项目列配置
  const chargeItemColumns: ProColumns<ChargeItemInfo>[] = [
    {
      title: "项目代码",
      dataIndex: "code",
      key: "code",
      width: 120,
    },
    {
      title: "项目名称",
      dataIndex: "name",
      key: "name",
      width: 150,
    },
    {
      title: "项目分类",
      dataIndex: "category",
      key: "category",
      width: 100,
      valueEnum: {
        挂号费: { text: "挂号费", status: "Processing" },
        检查费: { text: "检查费", status: "Success" },
        治疗费: { text: "治疗费", status: "Warning" },
        药品费: { text: "药品费", status: "Error" },
        材料费: { text: "材料费", status: "Default" },
      },
    },
    {
      title: "价格",
      dataIndex: "price",
      key: "price",
      width: 100,
      valueType: "money",
    },
    {
      title: "单位",
      dataIndex: "unit",
      key: "unit",
      width: 80,
    },
    {
      title: "项目描述",
      dataIndex: "description",
      key: "description",
      width: 200,
      ellipsis: true,
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 80,
      valueEnum: {
        正常: { text: "正常", status: "Success" },
        停用: { text: "停用", status: "Error" },
      },
    },
    {
      title: "创建时间",
      dataIndex: "createTime",
      key: "createTime",
      width: 150,
      valueType: "dateTime",
    },
    {
      title: "操作",
      key: "action",
      width: 150,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const handleAdd = () => {
    setModalType("add");
    setEditingRecord(null);
    setModalVisible(true);
  };

  const handleEdit = (record: any) => {
    setModalType("edit");
    setEditingRecord(record);
    setModalVisible(true);
  };

  const handleDelete = (id: string) => {
    Modal.confirm({
      title: "确认删除",
      content: "确定要删除这条记录吗？",
      onOk: () => {
        message.success("删除成功");
        actionRef.current?.reload();
      },
    });
  };

  const handleModalOk = async (values: any) => {
    try {
      if (modalType === "add") {
        message.success("新增成功");
      } else {
        message.success("编辑成功");
      }
      setModalVisible(false);
      actionRef.current?.reload();
    } catch (error) {
      message.error("操作失败");
    }
  };

  const renderModalForm = () => {
    if (activeTab === "organization") {
      return (
        <ProForm
          initialValues={editingRecord}
          onFinish={handleModalOk}
          modalProps={{
            title: modalType === "add" ? "新增机构信息" : "编辑机构信息",
            open: modalVisible,
            onCancel: () => setModalVisible(false),
          }}
        >
          <ProFormText
            name="name"
            label="机构名称"
            rules={[{ required: true, message: "请输入机构名称" }]}
          />
          <ProFormText
            name="creditCode"
            label="统一社会信用代码"
            rules={[{ required: true, message: "请输入统一社会信用代码" }]}
          />
          <ProFormTextArea
            name="address"
            label="机构地址"
            rules={[{ required: true, message: "请输入机构地址" }]}
          />
          <ProFormText
            name="contact"
            label="联系人"
            rules={[{ required: true, message: "请输入联系人" }]}
          />
          <ProFormText
            name="phone"
            label="联系电话"
            rules={[{ required: true, message: "请输入联系电话" }]}
          />
          <ProFormSelect
            name="type"
            label="机构类型"
            options={[
              { label: "三甲医院", value: "三甲医院" },
              { label: "二甲医院", value: "二甲医院" },
              { label: "一甲医院", value: "一甲医院" },
              { label: "专科医院", value: "专科医院" },
              { label: "社区医院", value: "社区医院" },
            ]}
            rules={[{ required: true, message: "请选择机构类型" }]}
          />
          <ProFormSelect
            name="status"
            label="状态"
            options={[
              { label: "正常", value: "正常" },
              { label: "停用", value: "停用" },
            ]}
            rules={[{ required: true, message: "请选择状态" }]}
          />
        </ProForm>
      );
    } else if (activeTab === "department") {
      return (
        <ProForm
          initialValues={editingRecord}
          onFinish={handleModalOk}
          modalProps={{
            title: modalType === "add" ? "新增科室信息" : "编辑科室信息",
            open: modalVisible,
            onCancel: () => setModalVisible(false),
          }}
        >
          <ProFormText
            name="code"
            label="科室代码"
            rules={[{ required: true, message: "请输入科室代码" }]}
          />
          <ProFormText
            name="name"
            label="科室名称"
            rules={[{ required: true, message: "请输入科室名称" }]}
          />
          <ProFormSelect
            name="type"
            label="科室类型"
            options={[
              { label: "临床科室", value: "临床科室" },
              { label: "医技科室", value: "医技科室" },
              { label: "行政科室", value: "行政科室" },
            ]}
            rules={[{ required: true, message: "请选择科室类型" }]}
          />
          <ProFormTextArea
            name="description"
            label="科室描述"
            rules={[{ required: true, message: "请输入科室描述" }]}
          />
          <ProFormSelect
            name="status"
            label="状态"
            options={[
              { label: "正常", value: "正常" },
              { label: "停用", value: "停用" },
            ]}
            rules={[{ required: true, message: "请选择状态" }]}
          />
        </ProForm>
      );
    } else if (activeTab === "chargeItem") {
      return (
        <ProForm
          initialValues={editingRecord}
          onFinish={handleModalOk}
          modalProps={{
            title: modalType === "add" ? "新增收费项目" : "编辑收费项目",
            open: modalVisible,
            onCancel: () => setModalVisible(false),
          }}
        >
          <ProFormText
            name="code"
            label="项目代码"
            rules={[{ required: true, message: "请输入项目代码" }]}
          />
          <ProFormText
            name="name"
            label="项目名称"
            rules={[{ required: true, message: "请输入项目名称" }]}
          />
          <ProFormSelect
            name="category"
            label="项目分类"
            options={[
              { label: "挂号费", value: "挂号费" },
              { label: "检查费", value: "检查费" },
              { label: "治疗费", value: "治疗费" },
              { label: "药品费", value: "药品费" },
              { label: "材料费", value: "材料费" },
            ]}
            rules={[{ required: true, message: "请选择项目分类" }]}
          />
          <ProFormText
            name="price"
            label="价格"
            rules={[{ required: true, message: "请输入价格" }]}
          />
          <ProFormText
            name="unit"
            label="单位"
            rules={[{ required: true, message: "请输入单位" }]}
          />
          <ProFormTextArea
            name="description"
            label="项目描述"
            rules={[{ required: true, message: "请输入项目描述" }]}
          />
          <ProFormSelect
            name="status"
            label="状态"
            options={[
              { label: "正常", value: "正常" },
              { label: "停用", value: "停用" },
            ]}
            rules={[{ required: true, message: "请选择状态" }]}
          />
        </ProForm>
      );
    }

    return null;
  };

  const tabItems = [
    {
      key: "organization",
      label: "机构信息",
      children: (
        <ProTable<OrganizationInfo>
          columns={organizationColumns}
          dataSource={mockOrganizationData}
          rowKey="id"
          actionRef={actionRef}
          search={false}
          toolBarRender={() => [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              新增机构
            </Button>,
          ]}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
          }}
        />
      ),
    },
    {
      key: "department",
      label: "科室信息",
      children: (
        <ProTable<DepartmentInfo>
          columns={departmentColumns}
          dataSource={mockDepartmentData}
          rowKey="id"
          actionRef={actionRef}
          search={false}
          toolBarRender={() => [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              新增科室
            </Button>,
          ]}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
          }}
        />
      ),
    },
    {
      key: "chargeItem",
      label: "收费项目",
      children: (
        <ProTable<ChargeItemInfo>
          columns={chargeItemColumns}
          dataSource={mockChargeItemData}
          rowKey="id"
          actionRef={actionRef}
          search={false}
          toolBarRender={() => [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              新增收费项目
            </Button>,
          ]}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
          }}
        />
      ),
    },
  ];

  return (
    <PageContainer
      title="基础信息管理"
      content="管理医疗机构的基础信息，包括机构信息、科室信息和收费项目信息"
    >
      <ProCard>
        <Tabs activeKey={activeTab} onChange={setActiveTab} items={tabItems} />
      </ProCard>
      {renderModalForm()}
    </PageContainer>
  );
};

export default BasicInfoManagement;
