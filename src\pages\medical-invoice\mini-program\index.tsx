import {
  <PERSON><PERSON>ontainer,
  ProCard,
  ProTable,
  ProForm,
  ProFormText,
  ProFormTextArea,
  ProFormSelect,
  ProFormUploadButton,
  ProFormColorPicker,
} from '@ant-design/pro-components';
import { Button, message, Modal, Space, Tabs, Card, Row, Col, QRCode, Image, Descriptions } from 'antd';
import { 
  QrcodeOutlined, 
  DownloadOutlined, 
  EyeOutlined,
  SettingOutlined,
  CopyOutlined,
  ShareAltOutlined,
  MobileOutlined,
} from '@ant-design/icons';
import React, { useState, useRef } from 'react';
import type { ActionType, ProColumns } from '@ant-design/pro-components';

// 定义数据类型
interface MiniProgramConfig {
  id: string;
  name: string;
  description: string;
  qrCodeUrl: string;
  accessUrl: string;
  logo: string;
  primaryColor: string;
  backgroundColor: string;
  welcomeText: string;
  contactInfo: string;
  status: string;
  createTime: string;
  updateTime: string;
}

interface AccessLog {
  id: string;
  patientName: string;
  patientPhone: string;
  accessTime: string;
  accessType: string;
  invoiceCount: number;
  userAgent: string;
  ipAddress: string;
}

const MiniProgram: React.FC = () => {
  const [activeTab, setActiveTab] = useState('config');
  const [modalVisible, setModalVisible] = useState(false);
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState<any>(null);
  const [modalType, setModalType] = useState<'add' | 'edit' | 'preview'>('add');
  const actionRef = useRef<ActionType>();

  // 模拟数据
  const mockConfigData: MiniProgramConfig[] = [
    {
      id: '1',
      name: '北京市第一人民医院取票小程序',
      description: '患者可通过扫描二维码快速获取医疗电子票据',
      qrCodeUrl: 'https://example.com/qr/hospital001',
      accessUrl: 'https://miniprogram.example.com/hospital001',
      logo: '/logo.png',
      primaryColor: '#1890ff',
      backgroundColor: '#f0f2f5',
      welcomeText: '欢迎使用北京市第一人民医院电子票据查询系统',
      contactInfo: '客服电话：010-12345678',
      status: '已启用',
      createTime: '2024-01-01 10:00:00',
      updateTime: '2025-01-10 09:00:00',
    },
  ];

  const mockAccessLogData: AccessLog[] = [
    {
      id: '1',
      patientName: '张三',
      patientPhone: '138****8001',
      accessTime: '2025-01-10 09:30:00',
      accessType: '微信扫码',
      invoiceCount: 2,
      userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)',
      ipAddress: '*************',
    },
    {
      id: '2',
      patientName: '李四',
      patientPhone: '139****8002',
      accessTime: '2025-01-10 10:15:00',
      accessType: '支付宝扫码',
      invoiceCount: 1,
      userAgent: 'Mozilla/5.0 (Android 10; Mobile)',
      ipAddress: '*************',
    },
  ];

  // 配置管理列配置
  const configColumns: ProColumns<MiniProgramConfig>[] = [
    {
      title: '小程序名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      ellipsis: true,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      width: 250,
      ellipsis: true,
    },
    {
      title: '访问地址',
      dataIndex: 'accessUrl',
      key: 'accessUrl',
      width: 200,
      ellipsis: true,
      render: (_, record) => (
        <a href={record.accessUrl} target="_blank" rel="noopener noreferrer">
          {record.accessUrl}
        </a>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      valueEnum: {
        '已启用': { text: '已启用', status: 'Success' },
        '已禁用': { text: '已禁用', status: 'Error' },
        '维护中': { text: '维护中', status: 'Warning' },
      },
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      width: 150,
      valueType: 'dateTime',
    },
    {
      title: '操作',
      key: 'action',
      width: 250,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handlePreview(record)}
          >
            预览
          </Button>
          <Button
            type="link"
            size="small"
            icon={<QrcodeOutlined />}
            onClick={() => handleShowQRCode(record)}
          >
            二维码
          </Button>
          <Button
            type="link"
            size="small"
            icon={<SettingOutlined />}
            onClick={() => handleEdit(record)}
          >
            配置
          </Button>
          <Button
            type="link"
            size="small"
            icon={<ShareAltOutlined />}
            onClick={() => handleShare(record)}
          >
            分享
          </Button>
        </Space>
      ),
    },
  ];

  // 访问日志列配置
  const accessLogColumns: ProColumns<AccessLog>[] = [
    {
      title: '患者姓名',
      dataIndex: 'patientName',
      key: 'patientName',
      width: 100,
    },
    {
      title: '手机号码',
      dataIndex: 'patientPhone',
      key: 'patientPhone',
      width: 120,
    },
    {
      title: '访问时间',
      dataIndex: 'accessTime',
      key: 'accessTime',
      width: 150,
      valueType: 'dateTime',
    },
    {
      title: '访问方式',
      dataIndex: 'accessType',
      key: 'accessType',
      width: 100,
      valueEnum: {
        '微信扫码': { text: '微信扫码', status: 'Success' },
        '支付宝扫码': { text: '支付宝扫码', status: 'Processing' },
        '直接访问': { text: '直接访问', status: 'Default' },
      },
    },
    {
      title: '获取票据数',
      dataIndex: 'invoiceCount',
      key: 'invoiceCount',
      width: 120,
      valueType: 'digit',
    },
    {
      title: 'IP地址',
      dataIndex: 'ipAddress',
      key: 'ipAddress',
      width: 120,
    },
    {
      title: '设备信息',
      dataIndex: 'userAgent',
      key: 'userAgent',
      width: 200,
      ellipsis: true,
      render: (_, record) => {
        const isMobile = record.userAgent.includes('Mobile') || record.userAgent.includes('iPhone');
        return (
          <span>
            {isMobile ? <MobileOutlined style={{ color: '#52c41a' }} /> : '💻'} 
            {isMobile ? ' 移动设备' : ' 桌面设备'}
          </span>
        );
      },
    },
  ];

  const handlePreview = (record: MiniProgramConfig) => {
    setEditingRecord(record);
    setModalType('preview');
    setPreviewModalVisible(true);
  };

  const handleShowQRCode = (record: MiniProgramConfig) => {
    Modal.info({
      title: '小程序二维码',
      width: 400,
      content: (
        <div style={{ textAlign: 'center', padding: '20px 0' }}>
          <QRCode value={record.accessUrl} size={200} />
          <div style={{ marginTop: 16 }}>
            <p>扫描二维码访问取票小程序</p>
            <Space>
              <Button 
                icon={<DownloadOutlined />}
                onClick={() => message.success('二维码下载成功')}
              >
                下载二维码
              </Button>
              <Button 
                icon={<CopyOutlined />}
                onClick={() => {
                  navigator.clipboard.writeText(record.accessUrl);
                  message.success('链接已复制到剪贴板');
                }}
              >
                复制链接
              </Button>
            </Space>
          </div>
        </div>
      ),
    });
  };

  const handleEdit = (record: MiniProgramConfig) => {
    setModalType('edit');
    setEditingRecord(record);
    setModalVisible(true);
  };

  const handleShare = (record: MiniProgramConfig) => {
    Modal.info({
      title: '分享小程序',
      content: (
        <div>
          <p><strong>小程序名称：</strong>{record.name}</p>
          <p><strong>访问链接：</strong>{record.accessUrl}</p>
          <p><strong>分享文案：</strong></p>
          <div style={{ backgroundColor: '#f5f5f5', padding: '12px', borderRadius: '4px' }}>
            {record.welcomeText}
            <br />
            点击链接或扫描二维码即可快速获取医疗电子票据。
            <br />
            {record.contactInfo}
          </div>
          <div style={{ marginTop: 16 }}>
            <Button 
              type="primary" 
              icon={<CopyOutlined />}
              onClick={() => message.success('分享内容已复制')}
            >
              复制分享内容
            </Button>
          </div>
        </div>
      ),
    });
  };

  const handleModalOk = async (values: any) => {
    try {
      if (modalType === 'add') {
        message.success('新增成功');
      } else {
        message.success('配置保存成功');
      }
      setModalVisible(false);
      actionRef.current?.reload();
    } catch (error) {
      message.error('操作失败');
    }
  };

  const renderPreviewModal = () => {
    if (!editingRecord) return null;

    return (
      <Modal
        title="小程序预览"
        open={previewModalVisible}
        onCancel={() => setPreviewModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setPreviewModalVisible(false)}>
            关闭
          </Button>,
        ]}
        width={400}
      >
        <div style={{ 
          backgroundColor: editingRecord.backgroundColor, 
          padding: '20px', 
          borderRadius: '8px',
          textAlign: 'center'
        }}>
          <div style={{ marginBottom: '16px' }}>
            <Image 
              src={editingRecord.logo} 
              alt="Logo" 
              width={60} 
              height={60}
              style={{ borderRadius: '8px' }}
              fallback="/logo.svg"
            />
          </div>
          <h3 style={{ color: editingRecord.primaryColor, marginBottom: '16px' }}>
            {editingRecord.name}
          </h3>
          <p style={{ marginBottom: '20px', color: '#666' }}>
            {editingRecord.welcomeText}
          </p>
          <div style={{ 
            backgroundColor: '#fff', 
            padding: '16px', 
            borderRadius: '8px',
            marginBottom: '16px'
          }}>
            <p><strong>请输入以下信息查询票据：</strong></p>
            <div style={{ textAlign: 'left', marginTop: '12px' }}>
              <p>• 手机号码</p>
              <p>• 身份证号</p>
              <p>• 就诊号（可选）</p>
            </div>
          </div>
          <p style={{ fontSize: '12px', color: '#999' }}>
            {editingRecord.contactInfo}
          </p>
        </div>
      </Modal>
    );
  };

  const renderConfigForm = () => (
    <Modal
      title={modalType === 'add' ? '新增小程序配置' : '编辑小程序配置'}
      open={modalVisible}
      onCancel={() => setModalVisible(false)}
      footer={null}
      width={600}
    >
      <ProForm
        initialValues={editingRecord}
        onFinish={handleModalOk}
      >
        <ProFormText
          name="name"
          label="小程序名称"
          rules={[{ required: true, message: '请输入小程序名称' }]}
        />
        <ProFormTextArea
          name="description"
          label="描述"
          rules={[{ required: true, message: '请输入描述' }]}
        />
        <ProFormText
          name="accessUrl"
          label="访问地址"
          rules={[{ required: true, message: '请输入访问地址' }]}
        />
        <ProFormUploadButton
          name="logo"
          label="Logo"
          max={1}
          fieldProps={{
            name: 'file',
            listType: 'picture-card',
          }}
        />
        <ProFormColorPicker
          name="primaryColor"
          label="主题色"
          rules={[{ required: true, message: '请选择主题色' }]}
        />
        <ProFormColorPicker
          name="backgroundColor"
          label="背景色"
          rules={[{ required: true, message: '请选择背景色' }]}
        />
        <ProFormTextArea
          name="welcomeText"
          label="欢迎文案"
          rules={[{ required: true, message: '请输入欢迎文案' }]}
        />
        <ProFormText
          name="contactInfo"
          label="联系信息"
          rules={[{ required: true, message: '请输入联系信息' }]}
        />
        <ProFormSelect
          name="status"
          label="状态"
          options={[
            { label: '已启用', value: '已启用' },
            { label: '已禁用', value: '已禁用' },
            { label: '维护中', value: '维护中' },
          ]}
          rules={[{ required: true, message: '请选择状态' }]}
        />
      </ProForm>
    </Modal>
  );

  const renderOverviewCards = () => {
    const totalAccess = mockAccessLogData.length;
    const todayAccess = mockAccessLogData.filter(log => 
      log.accessTime.startsWith('2025-01-10')
    ).length;
    const totalInvoices = mockAccessLogData.reduce((sum, log) => sum + log.invoiceCount, 0);

    return (
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <QrcodeOutlined style={{ fontSize: '32px', color: '#1890ff' }} />
              <div style={{ marginTop: 8 }}>
                <div style={{ fontSize: '24px', fontWeight: 'bold' }}>{mockConfigData.length}</div>
                <div style={{ color: '#666' }}>活跃小程序</div>
              </div>
            </div>
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <MobileOutlined style={{ fontSize: '32px', color: '#52c41a' }} />
              <div style={{ marginTop: 8 }}>
                <div style={{ fontSize: '24px', fontWeight: 'bold' }}>{totalAccess}</div>
                <div style={{ color: '#666' }}>总访问次数</div>
              </div>
            </div>
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <DownloadOutlined style={{ fontSize: '32px', color: '#fa8c16' }} />
              <div style={{ marginTop: 8 }}>
                <div style={{ fontSize: '24px', fontWeight: 'bold' }}>{totalInvoices}</div>
                <div style={{ color: '#666' }}>票据获取总数</div>
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    );
  };

  const tabItems = [
    {
      key: 'config',
      label: '小程序配置',
      children: (
        <>
          {renderOverviewCards()}
          <ProTable<MiniProgramConfig>
            columns={configColumns}
            dataSource={mockConfigData}
            rowKey="id"
            actionRef={actionRef}
            search={false}
            toolBarRender={() => [
              <Button
                key="add"
                type="primary"
                icon={<QrcodeOutlined />}
                onClick={() => {
                  setModalType('add');
                  setEditingRecord(null);
                  setModalVisible(true);
                }}
              >
                新增小程序
              </Button>,
            ]}
            pagination={false}
          />
        </>
      ),
    },
    {
      key: 'logs',
      label: '访问日志',
      children: (
        <ProTable<AccessLog>
          columns={accessLogColumns}
          dataSource={mockAccessLogData}
          rowKey="id"
          actionRef={actionRef}
          search={{
            labelWidth: 'auto',
          }}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
          }}
        />
      ),
    },
  ];

  return (
    <PageContainer
      title="取票小程序"
      content="管理患者取票小程序的配置、二维码生成和访问统计"
    >
      <ProCard>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
        />
      </ProCard>
      {renderPreviewModal()}
      {renderConfigForm()}
    </PageContainer>
  );
};

export default MiniProgram;
