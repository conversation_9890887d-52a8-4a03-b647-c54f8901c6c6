import {
  <PERSON><PERSON>ontainer,
  ProCard,
  ProTable,
  ProForm,
  ProFormText,
  ProFormTextArea,
  ProFormSelect,
  ProFormDigit,
  ProFormDatePicker,
  ProFormGroup,
} from '@ant-design/pro-components';
import { Button, message, Modal, Space, Tabs, Tag, Descriptions, Card } from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  PrinterOutlined,
  SearchOutlined,
  RedoOutlined,
  FileTextOutlined,
} from '@ant-design/icons';
import React, { useState, useRef } from 'react';
import type { ActionType, ProColumns } from '@ant-design/pro-components';

// 定义数据类型
interface MedicalInvoice {
  id: string;
  invoiceNo: string;
  invoiceCode: string;
  patientName: string;
  patientId: string;
  visitNo: string;
  department: string;
  doctor: string;
  totalAmount: number;
  issueDate: string;
  status: string;
  paymentMethod: string;
  items: InvoiceItem[];
}

interface InvoiceItem {
  id: string;
  itemCode: string;
  itemName: string;
  category: string;
  quantity: number;
  unitPrice: number;
  amount: number;
}

interface NonTaxInvoice {
  id: string;
  invoiceNo: string;
  invoiceType: string;
  payerName: string;
  payerIdCard: string;
  amount: number;
  purpose: string;
  issueDate: string;
  status: string;
  operator: string;
}

const InvoiceIssuing: React.FC = () => {
  const [activeTab, setActiveTab] = useState('medical');
  const [modalVisible, setModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState<any>(null);
  const [modalType, setModalType] = useState<'add' | 'edit' | 'reverse'>('add');
  const actionRef = useRef<ActionType>();

  // 模拟数据
  const mockMedicalInvoiceData: MedicalInvoice[] = [
    {
      id: '1',
      invoiceNo: 'PJ20250110001',
      invoiceCode: 'MZPJ001',
      patientName: '张三',
      patientId: '110101199001011234',
      visitNo: 'MZ20250110001',
      department: '内科',
      doctor: '李医生',
      totalAmount: 125.50,
      issueDate: '2025-01-10 09:30:00',
      status: '正常',
      paymentMethod: '微信支付',
      items: [
        {
          id: '1',
          itemCode: 'GHFEI001',
          itemName: '普通门诊挂号费',
          category: '挂号费',
          quantity: 1,
          unitPrice: 5.00,
          amount: 5.00,
        },
        {
          id: '2',
          itemCode: 'JCFEI001',
          itemName: '血常规检查',
          category: '检查费',
          quantity: 1,
          unitPrice: 25.00,
          amount: 25.00,
        },
        {
          id: '3',
          itemCode: 'YPFEI001',
          itemName: '阿莫西林胶囊',
          category: '药品费',
          quantity: 2,
          unitPrice: 47.75,
          amount: 95.50,
        },
      ],
    },
    {
      id: '2',
      invoiceNo: 'PJ20250110002',
      invoiceCode: 'MZPJ001',
      patientName: '李四',
      patientId: '110101199002021234',
      visitNo: 'MZ20250110002',
      department: '外科',
      doctor: '王医生',
      totalAmount: 85.00,
      issueDate: '2025-01-10 10:15:00',
      status: '已冲红',
      paymentMethod: '支付宝',
      items: [
        {
          id: '4',
          itemCode: 'GHFEI002',
          itemName: '专家门诊挂号费',
          category: '挂号费',
          quantity: 1,
          unitPrice: 15.00,
          amount: 15.00,
        },
        {
          id: '5',
          itemCode: 'ZLFEI001',
          itemName: '伤口处理',
          category: '治疗费',
          quantity: 1,
          unitPrice: 70.00,
          amount: 70.00,
        },
      ],
    },
  ];

  const mockNonTaxInvoiceData: NonTaxInvoice[] = [
    {
      id: '1',
      invoiceNo: 'FS20250110001',
      invoiceType: '非税收入票据',
      payerName: '张三',
      payerIdCard: '110101199001011234',
      amount: 500.00,
      purpose: '体检费',
      issueDate: '2025-01-10 14:00:00',
      status: '正常',
      operator: '财务人员A',
    },
  ];

  // 医疗票据列配置
  const medicalInvoiceColumns: ProColumns<MedicalInvoice>[] = [
    {
      title: '票据号码',
      dataIndex: 'invoiceNo',
      key: 'invoiceNo',
      width: 140,
    },
    {
      title: '患者姓名',
      dataIndex: 'patientName',
      key: 'patientName',
      width: 100,
    },
    {
      title: '就诊号',
      dataIndex: 'visitNo',
      key: 'visitNo',
      width: 120,
    },
    {
      title: '科室',
      dataIndex: 'department',
      key: 'department',
      width: 100,
    },
    {
      title: '医生',
      dataIndex: 'doctor',
      key: 'doctor',
      width: 100,
    },
    {
      title: '金额',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      width: 100,
      valueType: 'money',
    },
    {
      title: '开具时间',
      dataIndex: 'issueDate',
      key: 'issueDate',
      width: 150,
      valueType: 'dateTime',
    },
    {
      title: '支付方式',
      dataIndex: 'paymentMethod',
      key: 'paymentMethod',
      width: 100,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (_, record) => {
        const statusConfig = {
          '正常': { color: 'green', text: '正常' },
          '已冲红': { color: 'red', text: '已冲红' },
          '已作废': { color: 'gray', text: '已作废' },
        };
        const config = statusConfig[record.status as keyof typeof statusConfig];
        return <Tag color={config?.color}>{config?.text}</Tag>;
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 250,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<SearchOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>
          <Button
            type="link"
            size="small"
            icon={<PrinterOutlined />}
            onClick={() => handlePrint(record.id)}
          >
            打印
          </Button>
          {record.status === '正常' && (
            <Button
              type="link"
              size="small"
              danger
              icon={<RedoOutlined />}
              onClick={() => handleReverse(record)}
            >
              冲红
            </Button>
          )}
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  // 非税票据列配置
  const nonTaxInvoiceColumns: ProColumns<NonTaxInvoice>[] = [
    {
      title: '票据号码',
      dataIndex: 'invoiceNo',
      key: 'invoiceNo',
      width: 140,
    },
    {
      title: '票据类型',
      dataIndex: 'invoiceType',
      key: 'invoiceType',
      width: 120,
    },
    {
      title: '缴款人',
      dataIndex: 'payerName',
      key: 'payerName',
      width: 100,
    },
    {
      title: '身份证号',
      dataIndex: 'payerIdCard',
      key: 'payerIdCard',
      width: 150,
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      width: 100,
      valueType: 'money',
    },
    {
      title: '缴费事由',
      dataIndex: 'purpose',
      key: 'purpose',
      width: 120,
    },
    {
      title: '开具时间',
      dataIndex: 'issueDate',
      key: 'issueDate',
      width: 150,
      valueType: 'dateTime',
    },
    {
      title: '操作员',
      dataIndex: 'operator',
      key: 'operator',
      width: 100,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (_, record) => {
        const statusConfig = {
          '正常': { color: 'green', text: '正常' },
          '已冲红': { color: 'red', text: '已冲红' },
          '已作废': { color: 'gray', text: '已作废' },
        };
        const config = statusConfig[record.status as keyof typeof statusConfig];
        return <Tag color={config?.color}>{config?.text}</Tag>;
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            icon={<PrinterOutlined />}
            onClick={() => handlePrint(record.id)}
          >
            打印
          </Button>
          {record.status === '正常' && (
            <Button
              type="link"
              size="small"
              danger
              icon={<RedoOutlined />}
              onClick={() => handleReverse(record)}
            >
              冲红
            </Button>
          )}
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const handleAdd = () => {
    setModalType('add');
    setEditingRecord(null);
    setModalVisible(true);
  };

  const handleEdit = (record: any) => {
    setModalType('edit');
    setEditingRecord(record);
    setModalVisible(true);
  };

  const handleViewDetail = (record: MedicalInvoice) => {
    setEditingRecord(record);
    setDetailModalVisible(true);
  };

  const handleReverse = (record: any) => {
    Modal.confirm({
      title: '确认冲红',
      content: `确定要对票据 ${record.invoiceNo} 进行冲红操作吗？`,
      onOk: () => {
        message.success('冲红成功');
        actionRef.current?.reload();
      },
    });
  };

  const handlePrint = (id: string) => {
    message.success('打印任务已发送');
  };

  const handleDelete = (id: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这条记录吗？',
      onOk: () => {
        message.success('删除成功');
        actionRef.current?.reload();
      },
    });
  };

  const handleModalOk = async (values: any) => {
    try {
      if (modalType === 'add') {
        message.success('开具成功');
      } else {
        message.success('编辑成功');
      }
      setModalVisible(false);
      actionRef.current?.reload();
    } catch (error) {
      message.error('操作失败');
    }
  };

  const renderDetailModal = () => {
    if (!editingRecord) return null;

    return (
      <Modal
        title="票据详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="print" type="primary" icon={<PrinterOutlined />}>
            打印票据
          </Button>,
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>,
        ]}
        width={800}
      >
        <Card>
          <Descriptions title="基本信息" bordered column={2}>
            <Descriptions.Item label="票据号码">{editingRecord.invoiceNo}</Descriptions.Item>
            <Descriptions.Item label="票据代码">{editingRecord.invoiceCode}</Descriptions.Item>
            <Descriptions.Item label="患者姓名">{editingRecord.patientName}</Descriptions.Item>
            <Descriptions.Item label="身份证号">{editingRecord.patientId}</Descriptions.Item>
            <Descriptions.Item label="就诊号">{editingRecord.visitNo}</Descriptions.Item>
            <Descriptions.Item label="科室">{editingRecord.department}</Descriptions.Item>
            <Descriptions.Item label="医生">{editingRecord.doctor}</Descriptions.Item>
            <Descriptions.Item label="开具时间">{editingRecord.issueDate}</Descriptions.Item>
            <Descriptions.Item label="支付方式">{editingRecord.paymentMethod}</Descriptions.Item>
            <Descriptions.Item label="总金额">
              <span style={{ color: '#f50', fontSize: '16px', fontWeight: 'bold' }}>
                ¥{editingRecord.totalAmount}
              </span>
            </Descriptions.Item>
          </Descriptions>
          
          <div style={{ marginTop: 24 }}>
            <h4>收费明细</h4>
            <ProTable
              columns={[
                { title: '项目代码', dataIndex: 'itemCode', width: 100 },
                { title: '项目名称', dataIndex: 'itemName', width: 150 },
                { title: '分类', dataIndex: 'category', width: 100 },
                { title: '数量', dataIndex: 'quantity', width: 80, valueType: 'digit' },
                { title: '单价', dataIndex: 'unitPrice', width: 100, valueType: 'money' },
                { title: '金额', dataIndex: 'amount', width: 100, valueType: 'money' },
              ]}
              dataSource={editingRecord.items}
              rowKey="id"
              search={false}
              pagination={false}
              toolBarRender={false}
              size="small"
            />
          </div>
        </Card>
      </Modal>
    );
  };

  const tabItems = [
    {
      key: 'medical',
      label: '医疗电子票据',
      children: (
        <ProTable<MedicalInvoice>
          columns={medicalInvoiceColumns}
          dataSource={mockMedicalInvoiceData}
          rowKey="id"
          actionRef={actionRef}
          search={{
            labelWidth: 'auto',
          }}
          toolBarRender={() => [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              手动开票
            </Button>,
            <Button
              key="sync"
              icon={<FileTextOutlined />}
            >
              同步HIS数据
            </Button>,
          ]}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
          }}
        />
      ),
    },
    {
      key: 'nonTax',
      label: '非税收入票据',
      children: (
        <ProTable<NonTaxInvoice>
          columns={nonTaxInvoiceColumns}
          dataSource={mockNonTaxInvoiceData}
          rowKey="id"
          actionRef={actionRef}
          search={{
            labelWidth: 'auto',
          }}
          toolBarRender={() => [
            <Button
              key="add"
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              开具票据
            </Button>,
          ]}
          pagination={{
            defaultPageSize: 10,
            showSizeChanger: true,
          }}
        />
      ),
    },
  ];

  return (
    <PageContainer
      title="票据开具"
      content="开具医疗电子票据、非税收入票据等各类票据，支持票据冲红、打印和查询功能"
    >
      <ProCard>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
        />
      </ProCard>
      {renderDetailModal()}
    </PageContainer>
  );
};

export default InvoiceIssuing;
