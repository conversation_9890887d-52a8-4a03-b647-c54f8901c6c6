import {
  Page<PERSON>ontainer,
  ProCard,
  ProTable,
  ProForm,
  ProFormText,
  ProFormSelect,
  ProFormTextArea,
  ProFormSwitch,
} from '@ant-design/pro-components';
import { Button, message, Modal, Space, Tabs, Tag, Card, Row, Col, Statistic } from 'antd';
import { 
  SendOutlined, 
  MessageOutlined, 
  MobileOutlined,
  GlobalOutlined,
  QrcodeOutlined,
  SettingOutlined,
  EyeOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import React, { useState, useRef } from 'react';
import type { ActionType, ProColumns } from '@ant-design/pro-components';

// 定义数据类型
interface DeliveryRecord {
  id: string;
  invoiceNo: string;
  patientName: string;
  patientPhone: string;
  deliveryChannel: string;
  deliveryTime: string;
  deliveryStatus: string;
  retryCount: number;
  errorMessage?: string;
  operator: string;
}

interface DeliveryConfig {
  id: string;
  channel: string;
  enabled: boolean;
  config: any;
  lastUpdateTime: string;
  operator: string;
}

interface DeliveryStats {
  totalDeliveries: number;
  successDeliveries: number;
  failedDeliveries: number;
  smsDeliveries: number;
  appDeliveries: number;
  webDeliveries: number;
  qrDeliveries: number;
}

const InvoiceDelivery: React.FC = () => {
  const [activeTab, setActiveTab] = useState('records');
  const [modalVisible, setModalVisible] = useState(false);
  const [configModalVisible, setConfigModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState<any>(null);
  const [modalType, setModalType] = useState<'add' | 'edit' | 'config'>('add');
  const actionRef = useRef<ActionType>();

  // 模拟数据
  const mockDeliveryData: DeliveryRecord[] = [
    {
      id: '1',
      invoiceNo: 'PJ20250110001',
      patientName: '张三',
      patientPhone: '13800138001',
      deliveryChannel: '短信',
      deliveryTime: '2025-01-10 09:35:00',
      deliveryStatus: '成功',
      retryCount: 0,
      operator: '系统自动',
    },
    {
      id: '2',
      invoiceNo: 'PJ20250110002',
      patientName: '李四',
      patientPhone: '13800138002',
      deliveryChannel: 'APP推送',
      deliveryTime: '2025-01-10 10:20:00',
      deliveryStatus: '失败',
      retryCount: 2,
      errorMessage: '用户未安装APP',
      operator: '系统自动',
    },
    {
      id: '3',
      invoiceNo: 'PJ20250110003',
      patientName: '王五',
      patientPhone: '13800138003',
      deliveryChannel: '取票小程序',
      deliveryTime: '2025-01-10 11:00:00',
      deliveryStatus: '成功',
      retryCount: 0,
      operator: '患者自取',
    },
  ];

  const mockConfigData: DeliveryConfig[] = [
    {
      id: '1',
      channel: '短信',
      enabled: true,
      config: {
        provider: '阿里云短信',
        template: '您的医疗票据已开具，点击链接查看：{link}',
        signName: '北京市第一人民医院',
      },
      lastUpdateTime: '2025-01-01 10:00:00',
      operator: '系统管理员',
    },
    {
      id: '2',
      channel: 'APP推送',
      enabled: true,
      config: {
        appId: 'com.hospital.app',
        pushTitle: '票据通知',
        pushContent: '您有新的医疗票据，请查收',
      },
      lastUpdateTime: '2025-01-01 10:00:00',
      operator: '系统管理员',
    },
  ];

  const mockStatsData: DeliveryStats = {
    totalDeliveries: 1250,
    successDeliveries: 1180,
    failedDeliveries: 70,
    smsDeliveries: 800,
    appDeliveries: 300,
    webDeliveries: 100,
    qrDeliveries: 50,
  };

  // 交付记录列配置
  const deliveryColumns: ProColumns<DeliveryRecord>[] = [
    {
      title: '票据号码',
      dataIndex: 'invoiceNo',
      key: 'invoiceNo',
      width: 140,
    },
    {
      title: '患者姓名',
      dataIndex: 'patientName',
      key: 'patientName',
      width: 100,
    },
    {
      title: '手机号码',
      dataIndex: 'patientPhone',
      key: 'patientPhone',
      width: 120,
    },
    {
      title: '交付渠道',
      dataIndex: 'deliveryChannel',
      key: 'deliveryChannel',
      width: 120,
      render: (_, record) => {
        const channelConfig = {
          '短信': { color: 'blue', icon: <MessageOutlined />, text: '短信' },
          'APP推送': { color: 'green', icon: <MobileOutlined />, text: 'APP推送' },
          '官网查询': { color: 'orange', icon: <GlobalOutlined />, text: '官网查询' },
          '取票小程序': { color: 'purple', icon: <QrcodeOutlined />, text: '取票小程序' },
        };
        const config = channelConfig[record.deliveryChannel as keyof typeof channelConfig];
        return (
          <Tag color={config?.color} icon={config?.icon}>
            {config?.text}
          </Tag>
        );
      },
    },
    {
      title: '交付时间',
      dataIndex: 'deliveryTime',
      key: 'deliveryTime',
      width: 150,
      valueType: 'dateTime',
    },
    {
      title: '交付状态',
      dataIndex: 'deliveryStatus',
      key: 'deliveryStatus',
      width: 100,
      render: (_, record) => {
        const statusConfig = {
          '成功': { color: 'green', text: '成功' },
          '失败': { color: 'red', text: '失败' },
          '处理中': { color: 'blue', text: '处理中' },
          '待重试': { color: 'orange', text: '待重试' },
        };
        const config = statusConfig[record.deliveryStatus as keyof typeof statusConfig];
        return <Tag color={config?.color}>{config?.text}</Tag>;
      },
    },
    {
      title: '重试次数',
      dataIndex: 'retryCount',
      key: 'retryCount',
      width: 100,
      valueType: 'digit',
      render: (_, record) => (
        <span style={{ color: record.retryCount > 0 ? '#ff4d4f' : '#52c41a' }}>
          {record.retryCount}
        </span>
      ),
    },
    {
      title: '错误信息',
      dataIndex: 'errorMessage',
      key: 'errorMessage',
      width: 150,
      ellipsis: true,
      render: (_, record) => record.errorMessage || '-',
    },
    {
      title: '操作员',
      dataIndex: 'operator',
      key: 'operator',
      width: 100,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>
          {record.deliveryStatus === '失败' && (
            <Button
              type="link"
              size="small"
              icon={<ReloadOutlined />}
              onClick={() => handleRetry(record.id)}
            >
              重试
            </Button>
          )}
        </Space>
      ),
    },
  ];

  // 渠道配置列配置
  const configColumns: ProColumns<DeliveryConfig>[] = [
    {
      title: '交付渠道',
      dataIndex: 'channel',
      key: 'channel',
      width: 120,
      render: (_, record) => {
        const channelConfig = {
          '短信': { color: 'blue', icon: <MessageOutlined />, text: '短信' },
          'APP推送': { color: 'green', icon: <MobileOutlined />, text: 'APP推送' },
          '官网查询': { color: 'orange', icon: <GlobalOutlined />, text: '官网查询' },
          '取票小程序': { color: 'purple', icon: <QrcodeOutlined />, text: '取票小程序' },
        };
        const config = channelConfig[record.channel as keyof typeof channelConfig];
        return (
          <Tag color={config?.color} icon={config?.icon}>
            {config?.text}
          </Tag>
        );
      },
    },
    {
      title: '启用状态',
      dataIndex: 'enabled',
      key: 'enabled',
      width: 100,
      render: (_, record) => (
        <Tag color={record.enabled ? 'green' : 'red'}>
          {record.enabled ? '已启用' : '已禁用'}
        </Tag>
      ),
    },
    {
      title: '配置信息',
      key: 'configInfo',
      width: 200,
      render: (_, record) => {
        if (record.channel === '短信') {
          return `服务商：${record.config.provider}`;
        } else if (record.channel === 'APP推送') {
          return `应用ID：${record.config.appId}`;
        }
        return '查看详细配置';
      },
    },
    {
      title: '最后更新',
      dataIndex: 'lastUpdateTime',
      key: 'lastUpdateTime',
      width: 150,
      valueType: 'dateTime',
    },
    {
      title: '操作员',
      dataIndex: 'operator',
      key: 'operator',
      width: 100,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<SettingOutlined />}
            onClick={() => handleConfigEdit(record)}
          >
            配置
          </Button>
          <Button
            type="link"
            size="small"
            onClick={() => handleTestChannel(record.id)}
          >
            测试
          </Button>
        </Space>
      ),
    },
  ];

  const handleViewDetail = (record: DeliveryRecord) => {
    setEditingRecord(record);
    setModalVisible(true);
  };

  const handleRetry = (id: string) => {
    Modal.confirm({
      title: '确认重试',
      content: '确定要重新发送这个票据吗？',
      onOk: () => {
        message.success('重试任务已启动');
        actionRef.current?.reload();
      },
    });
  };

  const handleConfigEdit = (record: DeliveryConfig) => {
    setEditingRecord(record);
    setModalType('config');
    setConfigModalVisible(true);
  };

  const handleTestChannel = (id: string) => {
    message.loading('正在测试渠道连接...', 2);
    setTimeout(() => {
      message.success('渠道测试成功');
    }, 2000);
  };

  const handleBatchDelivery = () => {
    Modal.confirm({
      title: '批量交付',
      content: '确定要批量交付未发送的票据吗？',
      onOk: () => {
        message.success('批量交付任务已启动');
        actionRef.current?.reload();
      },
    });
  };

  const renderStatsCards = () => (
    <Row gutter={16} style={{ marginBottom: 24 }}>
      <Col span={6}>
        <Card>
          <Statistic
            title="总交付数"
            value={mockStatsData.totalDeliveries}
            valueStyle={{ color: '#1890ff' }}
            prefix={<SendOutlined />}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="成功交付"
            value={mockStatsData.successDeliveries}
            valueStyle={{ color: '#52c41a' }}
            suffix={`/ ${mockStatsData.totalDeliveries}`}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="失败交付"
            value={mockStatsData.failedDeliveries}
            valueStyle={{ color: '#ff4d4f' }}
            suffix={`/ ${mockStatsData.totalDeliveries}`}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="成功率"
            value={(mockStatsData.successDeliveries / mockStatsData.totalDeliveries * 100).toFixed(1)}
            valueStyle={{ color: '#52c41a' }}
            suffix="%"
          />
        </Card>
      </Col>
    </Row>
  );

  const renderChannelStats = () => (
    <Row gutter={16} style={{ marginBottom: 24 }}>
      <Col span={6}>
        <Card>
          <Statistic
            title="短信交付"
            value={mockStatsData.smsDeliveries}
            prefix={<MessageOutlined style={{ color: '#1890ff' }} />}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="APP推送"
            value={mockStatsData.appDeliveries}
            prefix={<MobileOutlined style={{ color: '#52c41a' }} />}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="官网查询"
            value={mockStatsData.webDeliveries}
            prefix={<GlobalOutlined style={{ color: '#fa8c16' }} />}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="小程序取票"
            value={mockStatsData.qrDeliveries}
            prefix={<QrcodeOutlined style={{ color: '#722ed1' }} />}
          />
        </Card>
      </Col>
    </Row>
  );

  const tabItems = [
    {
      key: 'records',
      label: '交付记录',
      children: (
        <>
          {renderStatsCards()}
          <ProTable<DeliveryRecord>
            columns={deliveryColumns}
            dataSource={mockDeliveryData}
            rowKey="id"
            actionRef={actionRef}
            search={{
              labelWidth: 'auto',
            }}
            toolBarRender={() => [
              <Button
                key="batch"
                type="primary"
                icon={<SendOutlined />}
                onClick={handleBatchDelivery}
              >
                批量交付
              </Button>,
              <Button
                key="refresh"
                icon={<ReloadOutlined />}
                onClick={() => actionRef.current?.reload()}
              >
                刷新
              </Button>,
            ]}
            pagination={{
              defaultPageSize: 10,
              showSizeChanger: true,
            }}
          />
        </>
      ),
    },
    {
      key: 'config',
      label: '渠道配置',
      children: (
        <ProTable<DeliveryConfig>
          columns={configColumns}
          dataSource={mockConfigData}
          rowKey="id"
          actionRef={actionRef}
          search={false}
          toolBarRender={() => [
            <Button
              key="add"
              type="primary"
              icon={<SettingOutlined />}
              onClick={() => {
                setModalType('add');
                setEditingRecord(null);
                setConfigModalVisible(true);
              }}
            >
              新增渠道
            </Button>,
          ]}
          pagination={false}
        />
      ),
    },
    {
      key: 'stats',
      label: '统计分析',
      children: (
        <>
          {renderStatsCards()}
          {renderChannelStats()}
          <Card title="渠道使用趋势" style={{ marginTop: 16 }}>
            <div style={{ textAlign: 'center', padding: '40px 0' }}>
              <p>这里可以展示各渠道的使用趋势图表</p>
              <p style={{ color: '#666' }}>包括每日交付量、成功率变化等</p>
            </div>
          </Card>
        </>
      ),
    },
  ];

  return (
    <PageContainer
      title="票据交付"
      content="管理票据的多渠道交付，包括短信、APP推送、官网查询和取票小程序等方式"
    >
      <ProCard>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
        />
      </ProCard>
    </PageContainer>
  );
};

export default InvoiceDelivery;
